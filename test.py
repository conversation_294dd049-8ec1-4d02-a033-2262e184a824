from maix import image, camera, display, app, uart, pinmap

import cv2

import numpy as np


# 初始化串口
pinmap.set_pin_function("A17", "UART0_RX")
pinmap.set_pin_function("A16", "UART0_TX")


device = "/dev/ttyS0"

serial = uart.UART(device, 9600)

# 初始化摄像头和显示器

cam = camera.Camera(320, 240, fps=80)

disp = display.Display()



# 预计算形态学核，避免重复创建
kernel_rect = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))

MIN_AREA = 800  # 适度降低最小面积阈值，提升远距离检测
MAX_AREA = 80000

# 透视矫正参数
ENABLE_PERSPECTIVE_CORRECTION = True    # 是否启用透视矫正
SHOW_CORRECTION_DEBUG = True           # 是否显示矫正对比点
PERSPECTIVE_TARGET_SIZE = 100          # 透视矫正的目标尺寸

# 移除了不再使用的函数：sort_rect_points, match_corners_by_distance, is_similar_rect
# 这些函数在优化后的算法中不再需要

def perspective_correction(rect_points, target_width=100, target_height=100):
    """
    完整的逆透视矫正 - 使用透视变换矩阵
    对检测到的矩形进行真正的透视变换矫正

    Args:
        rect_points: 矩形的四个顶点坐标 [(x1,y1), (x2,y2), (x3,y3), (x4,y4)]
        target_width: 矫正后的目标宽度
        target_height: 矫正后的目标高度

    Returns:
        corrected_center: 矫正后的中心点坐标 (x, y)
    """
    # 将输入点转换为numpy数组
    src_points = np.array(rect_points, dtype=np.float32)

    # 按顺序排列四个点：左上、右上、右下、左下
    def order_points(pts):
        # 计算质心
        cx = np.mean(pts[:, 0])
        cy = np.mean(pts[:, 1])

        # 按到质心的角度排序
        angles = np.arctan2(pts[:, 1] - cy, pts[:, 0] - cx)
        sorted_indices = np.argsort(angles)
        sorted_pts = pts[sorted_indices]

        # 找到左上角（x+y最小）
        sums = sorted_pts[:, 0] + sorted_pts[:, 1]
        min_idx = np.argmin(sums)

        # 重新排列，从左上角开始，按顺时针顺序
        final_pts = np.roll(sorted_pts, -min_idx, axis=0)

        return final_pts

    ordered_points = order_points(src_points)

    # 定义目标矩形的四个角点（标准矩形）
    dst_points = np.array([
        [0, 0],                           # 左上
        [target_width, 0],                # 右上
        [target_width, target_height],    # 右下
        [0, target_height]                # 左下
    ], dtype=np.float32)

    try:
        # 计算透视变换矩阵
        transform_matrix = cv2.getPerspectiveTransform(ordered_points, dst_points)

        # 计算逆变换矩阵（从标准矩形回到原图）
        inverse_transform = cv2.getPerspectiveTransform(dst_points, ordered_points)

        # 标准矩形的中心点
        standard_center = np.array([target_width/2, target_height/2, 1], dtype=np.float32)

        # 将标准矩形的中心点变换回原图坐标
        corrected_center_homogeneous = inverse_transform @ standard_center.reshape(3, 1)

        # 转换回笛卡尔坐标
        if corrected_center_homogeneous[2, 0] != 0:
            final_center = corrected_center_homogeneous[:2, 0] / corrected_center_homogeneous[2, 0]
        else:
            # 如果变换失败，回退到几何中心
            final_center = np.mean(ordered_points, axis=0)

        # 确保结果在合理范围内
        final_center[0] = np.clip(final_center[0], 0, 320)
        final_center[1] = np.clip(final_center[1], 0, 240)

        return tuple(map(int, final_center))

    except Exception as e:
        print(f"透视变换失败: {e}")
        # 回退到几何中心
        geometric_center = np.mean(src_points, axis=0)
        return tuple(map(int, geometric_center))

def is_rectangle(approx):
    if approx is None or len(approx) != 4 or not cv2.isContourConvex(approx):
        return False

    pts = np.array([point[0] for point in approx], dtype=np.float32)

    # 向量化计算所有角度
    def calculate_angles(points):
        # 计算所有向量
        v1 = np.roll(points, -1, axis=0) - points  # 当前点到下一点
        v2 = np.roll(points, 1, axis=0) - points   # 当前点到上一点

        # 向量长度
        norm1 = np.linalg.norm(v1, axis=1)
        norm2 = np.linalg.norm(v2, axis=1)

        # 避免除零
        valid = (norm1 > 0) & (norm2 > 0)
        angles = np.zeros(4)

        if np.any(valid):
            # 计算夹角
            cos_angles = np.sum(v1[valid] * v2[valid], axis=1) / (norm1[valid] * norm2[valid])
            cos_angles = np.clip(cos_angles, -1.0, 1.0)
            angles[valid] = np.arccos(cos_angles) * 180 / np.pi

        return angles

    angles = calculate_angles(pts)
    return np.all((angles > 60) & (angles < 120))  # 向量化比较

# 主循环

while not app.need_exit():

    try:
 

        img = cam.read()



        if img is None:

            continue
 
 
 
        try:
 
            img_raw = image.image2cv(img, copy=True)
 
        except Exception as e:
 
            print("图像转换失败:", e)
 
            continue
 
 
 
        rect_center = (-1, -1)  # 替换midpoints变量
 
 
 
        # 矩形检测与中点计算

        try:

            gray = cv2.cvtColor(img_raw, cv2.COLOR_BGR2GRAY)

            # 优化二值化参数，更适合远距离小目标检测
            bin_img = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_MEAN_C,
                                            cv2.THRESH_BINARY, 7, 3)

            closed = cv2.morphologyEx(bin_img, cv2.MORPH_CLOSE, kernel_rect)

            contours, _ = cv2.findContours(closed, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)

            # 按面积排序轮廓，优先处理较小的（目标是最小矩形）
            contours_with_area = [(cv2.contourArea(c), c) for c in contours]
            contours_with_area.sort(key=lambda x: x[0])  # 按面积从小到大排序

            img_h, img_w = img_raw.shape[:2]
            margin = 1  # 恢复原来的边界检查

            # 直接在检测过程中维护最小矩形
            min_rect = None
            min_area = float('inf')

            for area, contour in contours_with_area:
                # 面积检查
                if area < MIN_AREA:
                    continue
                if area > MAX_AREA:
                    break  # 由于已排序，后面的都会更大

                # 边界检查（使用预计算的图像尺寸）
                x, y, w, h = cv2.boundingRect(contour)
                if x < margin or y < margin or x + w > img_w - margin or y + h > img_h - margin:
                    continue

                # 自适应轮廓近似精度（保留这个优化，因为它不会引起误检）
                perimeter = cv2.arcLength(contour, True)
                if perimeter < 80:  # 远距离小目标使用更精细的近似
                    epsilon = 0.015 * perimeter
                else:
                    epsilon = 0.03 * perimeter
                approx = cv2.approxPolyDP(contour, epsilon, True)

                if is_rectangle(approx):
                    rect = [tuple(map(int, pt[0])) for pt in approx]
                    rect_area = cv2.contourArea(np.array(rect, dtype=np.int32))

                    if rect_area < min_area:
                        min_area = rect_area
                        min_rect = rect

            # 绘制最小矩形和计算中心点
            if min_rect:
                # 绘制最小矩形轮廓
                cv2.drawContours(img_raw, [np.array(min_rect, dtype=np.int32)], -1, (0, 255, 0), 2)

                # 原始几何中心点
                original_center_x = sum(pt[0] for pt in min_rect) // 4
                original_center_y = sum(pt[1] for pt in min_rect) // 4
                original_center = (original_center_x, original_center_y)

                # 透视矫正
                if ENABLE_PERSPECTIVE_CORRECTION:
                    try:
                        corrected_center = perspective_correction(min_rect, PERSPECTIVE_TARGET_SIZE, PERSPECTIVE_TARGET_SIZE)
                        rect_center = corrected_center

                        # 显示两个中心点对比（用于调试）
                        if SHOW_CORRECTION_DEBUG:
                            cv2.circle(img_raw, original_center, 4, (0, 0, 255), 2)      # 红色圆圈：原始中心
                            cv2.circle(img_raw, rect_center, 3, (0, 255, 255), -1)      # 青色实心：矫正中心
                            # 添加文字标注
                            cv2.putText(img_raw, "Orig", (original_center[0]+5, original_center[1]-5),
                                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 255), 1)
                            cv2.putText(img_raw, "Corr", (rect_center[0]+5, rect_center[1]-5),
                                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 255), 1)
                        else:
                            cv2.circle(img_raw, rect_center, 2, (0, 255, 255), -1)      # 青色实心：矫正中心

                    except Exception as e:
                        print(f"透视矫正失败: {e}")
                        rect_center = original_center
                        cv2.circle(img_raw, rect_center, 2, (0, 255, 255), -1)
                else:
                    rect_center = original_center
                    cv2.circle(img_raw, rect_center, 2, (0, 255, 255), -1)  # 青色小实心圆
            else:
                rect_center = (-1, -1)

        except Exception as e:

            print("矩形检测异常:", e)
 
 
 
        # 发送矩形中心点坐标

        try:
            if rect_center[0] >= 0 and rect_center[1] >= 0:
                message = f":{rect_center[0]},{rect_center[1]},0#"
                print(f"矩形中心点: ({rect_center[0]}, {rect_center[1]}) [阈值: {current_min_area}]")
            else:
                message = f":0,0,0#"
                print(f"矩形中心点: (0, 0) [未检测帧数: {no_detection_frames}, 阈值: {current_min_area}]")

            serial.write(message.encode('utf-8'))

        except Exception as e:

            print("串口发送异常:", e)
 
 
 
        # 显示图像

        try:
            img_show = image.cv2image(img_raw, copy=False)

            disp.show(img_show)

        except Exception as e:

            print("图像显示失败:", e)
 
 
    except Exception as e:
 
        print("主循环异常:", e)
